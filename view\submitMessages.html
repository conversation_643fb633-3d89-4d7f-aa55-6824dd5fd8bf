<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>提交留言</title>
    <link rel="stylesheet" href="../css/vant.css" />
    <style type="text/css">
      * {
        margin: 0;
        padding: 0;
      }

      body {
        background: #fff;
      }

      .body_box {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .LeavingMagess_box {
        background: #fff;
        width: 95%;
        margin: -100px auto 0;
        border-radius: 10px;
        padding-top: 30px;
        position: relative;
      }

      .LeavingMagess_img {
        position: absolute;
        right: 1.25vw;
        top: -10vw;
        width: 20vw;
        height: 20vw;
      }

      .form {
        padding: 30rpx;
        background: #fff;
        margin: 20px;
        border-radius: 10px;
      }

      .form_item {
        margin-bottom: 25px;
      }

      .form_label {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .form_input {
        width: 100%;
        height: 35px;
        font-size: 14px;
        border: none;
        border-bottom: 1px solid #ccd;
        padding: 5px;
      }

      .input_box {
        margin: 15px;
        padding-bottom: 10px;
        display: flex;
        border-bottom: 1px solid rgba(153, 153, 153, 0.2);
      }

      .input_box_title {
        color: #696969;
      }

      .input_box_titles {
        color: #696969;
        margin-right: 12px;
      }

      .input {
        border: 0;
        margin-left: 10px;
        flex: 1;
        color: #000;
        background: #fff;
      }

      .form_textarea {
        width: 100%;
        font-size: 14px;
        border: none;
        border-bottom: 1px solid #ccd;
        padding: 10px;
      }

      .form_upload {
        display: inline-block;
        width: 200rpx;
        padding: 8px 20px;
        background-color: #4a90e2;
        color: #fff;
        border-radius: 5px;
        font-size: 14px;
        text-align: center;
        margin-left: 10px;
      }

      .form_submit {
        display: block;
        padding: 10px;
        background-color: #4a93ff;
        color: #fff;
        border-radius: 10px;
        font-size: 18px;
        text-align: center;
        cursor: pointer;
      }

      .form_submit:hover {
        background-color: #3f81c7;
      }

      [v-cloak] {
        display: none;
      }

      ._p {
        position: fixed;
        bottom: 50vh;
        left: 50%;
        margin-right: -50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        max-width: 66vw;
        min-width: 100px;
        text-align: center;
        line-height: 1.5;
        padding: 10px;
        border-radius: 5px;
        background-color: rgba(0, 0, 0, 0.5);
        font-size: 16px;
        color: white;
        transition: all 0.2s;
      }
    </style>
  </head>

  <body>
    <div class="body_box" id="app">
      <img src="../img/tjbg.png" alt="" style="width: 100%; height: 100%" />
      <div class="LeavingMagess_box">
        <img src="../img/tjimg.png" class="LeavingMagess_img" alt="" />
        <div class="form">
          <div class="form_item">
            <div class="form_label">反映对象(代表/站点)</div>
            <input class="form_input" type="text" name="feedback" required placeholder="反映对象(代表/站点)" v-model="feedback" disabled />
          </div>
          <div class="form_item">
            <div class="form_label">
              您的姓名
              <span style="color: red; margin-left: 5px">*</span>
            </div>
            <input class="form_input" type="text" name="name" required placeholder="您的姓名" v-model="name" disabled />
          </div>
          <div class="form_item">
            <div class="form_label">
              您的电话
              <span style="color: red; margin-left: 5px">*</span>
            </div>
            <input class="form_input" type="text" name="phone" required placeholder="您的电话" v-model="phone" disabled />
          </div>
          <div class="form_item" v-if="areaId == '370202'" v-cloak>
            <div class="form_label">
              问题位置
              <span style="color: red; margin-left: 5px">*</span>
            </div>
            <div class="input_box" @click="siteShow = true">
              <div class="input_box_title input_box_titles">地理位置</div>
              <span style="color: #696969; font-size: 14px">{{location?location:'请选择街道'}}</span>
            </div>
            <div class="input_box" @click="selectCommunity">
              <div class="input_box_title input_box_titles">所属社区</div>
              <span style="color: #696969; font-size: 14px">{{community?community:'请选择社区'}}</span>
            </div>
            <div class="input_box">
              <div class="input_box_title">详细地址</div>
              <input class="input" type="text" placeholder="请输入详细地址" v-model="detailLocation" />
            </div>
          </div>
          <div class="form_item">
            <div class="form_label">
              建议或意见内容
              <span style="color: red; margin-left: 5px">*</span>
            </div>
            <textarea class="form_textarea" name="content" id="" cols="30" rows="10" required placeholder="建议或意见内容" v-model="content"></textarea>
          </div>
          <!-- <div class="form_item">
          <div class="form_label">上传图片<span
              style="color: #999999;font-size: 14px;margin-left: 18px;">(允许上传8张图片，单张照片不超过10M)</span></div>
          <div class="form_upload" @click="selectImage">选择图片</div>
          <input type="file" id="imageInput" style="display: none" @change="handleImageUpload">
          <div class="form_upload_preview">
            <img src="" alt="">
          </div>
        </div> -->
          <div class="form_submit" @click="submit">提交</div>
        </div>
      </div>
      <van-action-sheet v-model="siteShow" style="height: 40%">
        <div class="content">
          <van-picker :columns="columns" @confirm="onConfirm" @cancel="siteShow = false" show-toolbar />
        </div>
      </van-action-sheet>
      <van-action-sheet v-model="siteShows" style="height: 40%">
        <div class="content">
          <van-picker :columns="columnss" @confirm="onConfirms" @cancel="siteShows = false" show-toolbar />
        </div>
      </van-action-sheet>
      <div v-if="showCustomPopup" class="CustomPopup">
        <p class="_p" v-cloak>{{textDialog}}</p>
      </div>
    </div>
    <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
    <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
    <script defer type="text/javascript" src="../js/aes.js"></script>
    <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/vant.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
    <script>
      var privateKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
      var publicStreetInfoKey = '04611f64eabb501be326f2354c2bad53d253c3071959923fb288d5a852f59ac3dac5611f32a42b76055715a96a4ce3e9cf25fa8bb7117e44df10c72c8c25c781b6' //提交留言公钥
      var publicMessageAddKey = '04a5248ce422f726b70a9e8b3e431557340e3c08a64ceb2bf38a5816e9945af4316383c5e83bec2c48146ccc54de97f78ccfc8c416d4a51d7b48dfb27f4863d9d5' //提交留言公钥
      var title = ''
      var studioId = ''
      var userId = ''
      var ticket = ''
      var app = new Vue({
        el: '#app',
        data: {
          showCustomPopup: false,
          textDialog: '',
          feedback: 'feedback',
          name: JSON.parse(localStorage.getItem('gname')),
          phone: JSON.parse(localStorage.getItem('gphone')),
          name_o: '',
          phone_o: '',
          title: '',
          content: '',
          detailLocation: '',
          areaId: localStorage.getItem('areaId'),
          siteShow: false,
          siteShows: false,
          location: '',
          community: '',
          longitude: '',
          latitude: '',
          columns: [],
          columnss: [],
          siteList: []
        },
        async mounted() {
          var vConsole = new VConsole() // 初始化
          console.log('this.name===>', this.name)
          console.log('this.phone===>', this.phone)
          if (this.name) {
            this.name_o = this.name
            this.name = this.maskName(this.name)
          }
          if (this.phone) {
            this.phone_o = this.phone
            this.phone = this.maskMobile(this.phone)
          }
          const urlParams = new URLSearchParams(window.location.search)
          title = urlParams.get('title')
          studioId = urlParams.get('studioId')
          userId = urlParams.get('userId')
          this.feedback = title
          if (this.areaId === '370202') {
            this.getList()
          }
          this.getInterfaceTicket()
        },
        methods: {
          maskName(name) {
            if (!name) {
              return ''
            }
            var len = name.length
            if (len <= 1) {
              return name
            }
            var mask = '*'.repeat(len - 1)
            return mask + name.charAt(len - 1)
          },
          maskMobile(mobile) {
            return mobile.slice(0, 3) + '*'.repeat(6) + mobile.slice(-2)
          },
          // 获取interfaceTicket
          getInterfaceTicket() {
            lightAppJssdk.user.getTicketWithoutDialog({
              success: function (data) {
                // alert('data===>' + JSON.stringify(data))
                if (data == '未登录') {
                  onLoginApp() //APP用户未登录，调用登录页面
                } else {
                  var jsonData = JSON.parse(data)
                  if (jsonData.retcode == '000000') {
                    var dataValueObj = JSON.parse(jsonData.data)
                    ticket = dataValueObj.ticket
                  }
                }
              },
              fail: function (data) {
                // 错误返回
                console.log(JSON.stringify(data))
              }
            })
          },
          // 提交
          submit() {
            if (!this.name) {
              this.textDialog = '请输入您的姓名'
              this.showCustomPopup = true
              setTimeout(() => {
                this.showCustomPopup = false
              }, 2000)
              return
            }
            if (!this.phone) {
              this.textDialog = '请输入您的电话'
              this.showCustomPopup = true
              setTimeout(() => {
                this.showCustomPopup = false
              }, 2000)
              return
            }
            if (this.areaId === '370202') {
              if (!this.location) {
                this.textDialog = '请选择街道'
                this.showCustomPopup = true
                setTimeout(() => {
                  this.showCustomPopup = false
                }, 2000)
                return
              }
              if (!this.community) {
                this.textDialog = '请选择社区'
                this.showCustomPopup = true
                setTimeout(() => {
                  this.showCustomPopup = false
                }, 2000)
                return
              }
              if (!this.detailLocation) {
                this.textDialog = '请输入详细地址'
                this.showCustomPopup = true
                setTimeout(() => {
                  this.showCustomPopup = false
                }, 2000)
                return
              }
            }
            if (!this.content) {
              this.textDialog = '请输入建议或意见内容'
              this.showCustomPopup = true
              setTimeout(() => {
                this.showCustomPopup = false
              }, 2000)
              return
            }
            console.log('都输入了，此处调用接口')
            var that = this
            var appid = 'dbllztuhek'
            var interfaceid = 'qdrdllzMessageAdd'
            var interfacecontent = {
              name: this.name_o,
              title: this.content,
              mobile: this.phone_o,
              content: this.content,
              studioId: studioId,
              longitude: this.longitude,
              latitude: this.latitude,
              position: this.detailLocation,
              ssjg: this.location,
              linkPhone: this.community,
              shequ: this.content,
              lat: this.phone_o,
              areaId: this.areaId,
              userId: userId
            }
            console.log('新增留言参数', interfacecontent)
            let extraData = {
              header: { 'u-login-areaId': '370200' },
              verificationHeaders: {
                interfaceTicket: ticket
              } //二次核验请求头
            }
            console.log('extraData===>' + JSON.stringify(extraData))
            let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicMessageAddKey)
            vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
              try {
                var ret = JSON.parse(SM.decrypt(res, privateKey))
                console.log('留言新增是否成功', ret)
                if (ret && ret.errcode == 200) {
                  that.textDialog = '留言成功,请等待审核！'
                  that.showCustomPopup = true
                  setTimeout(() => {
                    that.showCustomPopup = false
                    window.history.back()
                  }, 2000)
                } else {
                  that.textDialog = '接口异常,请联系管理员！'
                  that.showCustomPopup = true
                  setTimeout(() => {
                    that.showCustomPopup = false
                  }, 2000)
                }
              } catch (error) {
                // 在这里处理异常情况
                console.log('解析JSON时发生错误：', error)
                that.submit()
              }
            })
          },
          getList() {
            var that = this
            let headers = {
              'U-Login-Areaid': '370200'
            }
            var interfacecontent = {}
            let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStreetInfoKey)
            let fdDqtq = new FormData()
            fdDqtq.append('app_id', 'dbllztuhek')
            fdDqtq.append('interface_id', 'qdrdllzProblemLocation')
            fdDqtq.append('version', '1.0')
            fdDqtq.append('biz_content', biz_content)
            fdDqtq.append('charset', 'utf-8')
            fdDqtq.append('timestamp', new Date().valueOf())
            fdDqtq.append('origin', '1')
            fdDqtq.append('sign', 'signResult')
            fdDqtq.append('header', JSON.stringify(headers))
            $.ajax({
              url: 'https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway',
              type: 'post',
              dataType: 'json',
              data: fdDqtq,
              contentType: false,
              processData: false,
              cache: false,
              success: function (data) {
                try {
                  var ret = JSON.parse(SM.decrypt(data.data, privateKey))
                  console.log('获取问题位置：', ret)
                  const uniqueElements = {}
                  ret.data.forEach(element => {
                    const { title } = element
                    if (!uniqueElements[title]) {
                      uniqueElements[title] = element
                    }
                  })
                  console.log('uniqueElements==>', uniqueElements)
                  that.siteList = Object.values(uniqueElements)
                  that.columns = that.siteList.map(item => item.title)
                } catch (error) {
                  // 在这里处理异常情况
                  console.log('解析JSON时发生错误：', error)
                  that.getList()
                }
              },
              error: function (data) {
                console.log('请求失败了！', data)
              }
            })
          },
          selectCommunity() {
            console.log('selectCommunity')
            if (this.location === '') {
              this.textDialog = '请先选择街道'
              this.showCustomPopup = true
              setTimeout(() => {
                this.showCustomPopup = false
              }, 2000)
              return
            }
            this.siteShows = true
          },
          onConfirm(e) {
            console.log('onConfirm', e)
            this.location = e
            this.siteShow = false
            this.columnss = this.siteList.find(item => item.title === e).childData
            this.columnss = this.columnss.map(item => {
              return {
                text: item.community,
                latitude: item.latitude,
                longitude: item.longitude
              }
            })
            this.community = ''
          },
          onConfirms(e) {
            console.log('onConfirm', e)
            this.siteShows = false
            this.latitude = e.latitude
            this.longitude = e.longitude
            this.community = e.text
          }
          // 打开选择图片
          // selectImage () {
          //   document.getElementById('imageInput').click();
          // },
          // 上传图片处理
          // handleImageUpload (event) {
          //   const file = event.target.files[0];
          //   console.log('file===>>>>>>>>>>>', file);
          // }
        }
      })
    </script>
  </body>
</html>

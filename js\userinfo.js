const appword = "aKd20dbGdFvmuwrt" // 固定值

window.onload = function () {
	getUserInfo()
}

function getUserInfo () {
	lightAppJssdk.user.getUserInfoWithEncryptedParamByAppId({
		appId: "dbllzsfrzdjqwscuphaz", // 接入方在成功创建应用后自动生成
		success: function (data) {
			if (data == '未登录') onLoginApp()
			else {
				if (typeof data == 'string')
					data = JSON.parse(data)
				// 对于该方法的返回结果,需要进行两步解密才能得到明文用户信息,
				// 先对返回结果进行sm2解密,需要用到 接入方在创建应用时,选择应用的加密方式为sm2,并成功创建应用后,拿到的应用私钥来进行解密
				const sm2_privateKey = '00a5496e4f84bae7db3921696ed8f059ff2c330659395d4e726b767cc84b6c6409'
				let sm2_encrypt_result = data.data
				let sm2_decrypt_result = sm2_Decrypt(sm2_encrypt_result, sm2_privateKey)
				if (typeof sm2_decrypt_result == 'string')
					sm2_decrypt_result = JSON.parse(sm2_decrypt_result)

				// 其次,对sm2解密后的结果进行 aes解密
				// aes解密需要用到 appword , 为固定值,使用示例代码中的即可
				let aes_encrypt_result = sm2_decrypt_result.data
				let aes_decrypt_result = aes_Decrypt(aes_encrypt_result, appword)
				localStorage.setItem("gname", JSON.stringify(JSON.parse(aes_decrypt_result).name))
				localStorage.setItem("gphone", JSON.stringify(JSON.parse(aes_decrypt_result).mobile))
			}
		},
		fail: function (data) {

		}
	});
}

/**
 * 使用jssdk调用登录页面
 */
function onLoginApp () {
	lightAppJssdk.user.loginapp({
		success: function (data) {
			if (data == "未登录") {
				//取消登录或登录失败，关闭页面
				oncloseWindow()
			} else {
				getUserInfo()
			}
		},
		fail: function (data) {
			//关闭页面
			oncloseWindow()
		}
	})
}

/**
 * 关闭容器
 */
function oncloseWindow () {
	lightAppJssdk.navigation.close({
		success: function (data) { },
		fail: function (data) { }
	})
}

// aes解密
function aes_Decrypt (word, key) {
	var key = CryptoJS.enc.Utf8.parse(key) //转为128bit
	var srcs = CryptoJS.enc.Hex.parse(word) //转为16进制
	var str = CryptoJS.enc.Base64.stringify(srcs) //变为Base64编码的字符串
	var decrypt = CryptoJS.AES.decrypt(str, key, {
		mode: CryptoJS.mode.ECB,
		spadding: CryptoJS.pad.Pkcs7
	})
	return decrypt.toString(CryptoJS.enc.Utf8)
}

// sm2解密,需要创建加密应用时的私钥
function sm2_Decrypt (word, key) {
	return SM.decrypt(word, key)
}

// let urlCreatesign = urldomaincreatesign
// let urlGateway = urldomaingateway
// const appmark = "sdzwapp"
// const appword = "aKd20dbGdFvmuwrt"
// let userType = "1" //用户类型  1个人   2法人

// window.onload = function () {
// 	getUserInfo()
// }

// /**
//  * 通过jssdk方法获取APP本地用户票据
//  */
// function getUserInfo () {
// 	lightAppJssdk.user.getTicket({
// 		success: function (data) {
// 			//成功回调
// 			console.log(JSON.stringify(data))
// 			if (data == "未登录") {
// 				onLoginApp() //APP用户未登录，调用登录页面
// 			} else {
// 				var jsonData = JSON.parse(data)
// 				if (jsonData.retcode == "000000") {
// 					var dataValueObj = JSON.parse(jsonData.data)
// 					var ticket = dataValueObj.ticket //解析出用户票据信息
// 					userType = dataValueObj.usertype //解析保存用户类型（个人法人）
// 					getUserToken(ticket)
// 				}
// 			}
// 		},
// 		fail: function (data) {
// 			//错误返回
// 			console.log(JSON.stringify(data))
// 		}
// 	})
// }

// /**
//  * 使用jssdk调用登录页面
//  */
// function onLoginApp () {
// 	lightAppJssdk.user.loginapp({
// 		success: function (data) {
// 			if (data == "未登录") {
// 				//取消登录或登录失败，关闭页面
// 				oncloseWindow()
// 			} else {
// 				var dataObj = JSON.parse(data)
// 				if (dataObj.retcode == "000000") {
// 					//登录成功，返回用户的票据和用户类型
// 					var dataValueObj = JSON.parse(dataObj.data)
// 					getUserToken(dataValueObj.ticket) //解析出用户票据信息
// 					userType = jsonData.usertype //解析保存用户类型（个人法人）
// 				} else {
// 					//关闭页面
// 					oncloseWindow()
// 				}
// 			}
// 		},
// 		fail: function (data) {
// 			//关闭页面
// 			oncloseWindow()
// 		}
// 	})
// }

// /**
//  * 通过用户票据获取用户的令牌数据，票据使用一次后失效，token时效性较长
//  */
// function getUserToken (ticket) {
// 	var params = '{"ticket":"' + ticket + '"}'
// 	const interfaceContent = {
// 		app_id: appmark,
// 		servicename: "ticketValidate",
// 		params: params
// 	}

// 	vaildInterfacefn("jisnzjk", "ticketvalidate", JSON.stringify(interfaceContent), "2", "https://" + urlCreatesign, "https://" + urlGateway).then((value) => {
// 		const data = JSON.parse(value)
// 		if (data.retcode == "000000") {
// 			const userToken = JSON.parse(data.data).token
// 			if (userType == "1") {
// 				//个人类型获取用户信息
// 				getUserInfoByToken(userToken)
// 			} else {
// 				//法人类型获取用户信息
// 				findCorUserByToken(userToken)
// 			}
// 		} else {
// 			lightAppJssdk.notification.alert({
// 				title: "提示", //可传空
// 				message: data.msg,
// 				buttonName: "确认",
// 				success: function (data) {
// 					oncloseWindow()
// 				}
// 			})
// 		}
// 	})
// }

// /**
//  * 根据令牌获取个人完整信息
//  */
// function getUserInfoByToken (userToken) {
// 	const params = '{"token":"' + userToken + '"}'
// 	const interfaceContent = {
// 		app_id: appmark,
// 		servicename: "findOutsideUserByToken",
// 		params: params
// 	}

// 	try {
// 		vaildInterfacefn("jisnzjk", "findoutsideuserbytoken", JSON.stringify(interfaceContent), "2", "https://" + urlCreatesign, "https://" + urlGateway).then((value) => {
// 			const data = JSON.parse(value)
// 			if (data.retcode == "000000") {
// 				const userInfo = data.data
// 				const userObj = JSON.parse(userInfo)
// 				// alert('userObj==>' + JSON.stringify(userObj))
// 				// localStorage.setItem("gisauthuser",userObj.isauthuser)
// 				// localStorage.setItem("gid",userObj.loginname)
// 				localStorage.setItem("gname", userObj.name)
// 				localStorage.setItem("gphone", userObj.mobile)
// 				// localStorage.setItem("gcardid",userObj.papersnumber)

// 			} else {
// 			}
// 		})
// 	} catch (error) { }
// }

// /**
//  * 根据令牌获取完整法人信息
//  */
// function findCorUserByToken (userToken) {
// 	try {
// 		const params = '{"token":"' + userToken + '"}'
// 		const interfaceContent = {
// 			app_id: appmark,
// 			servicename: "findCorUserByToken",
// 			params: params
// 		}
// 		vaildInterfacefn("jisnzjk", "findcoruserytoken", JSON.stringify(interfaceContent), "2", "https://" + urlCreatesign, "https://" + urlGateway).then((value) => {
// 			const data = JSON.parse(value)

// 			if (data.retcode == "000000") {
// 				const userInfo = JSON.stringify(data.data)
// 				localStorage.setItem("userinfo", userInfo)
// 			} else {
// 			}
// 		})
// 	} catch (err) { }
// }

// /**
//  * 关闭容器
//  */
// function oncloseWindow () {
// 	lightAppJssdk.navigation.close({
// 		success: function (data) { },
// 		fail: function (data) { }
// 	})
// }

// /**
//  * AES解密
//  */
// function Decrypt (word, key) {
// 	var key = CryptoJS.enc.Utf8.parse(key) //转为128bit
// 	var srcs = CryptoJS.enc.Hex.parse(word) //转为16进制
// 	var str = CryptoJS.enc.Base64.stringify(srcs) //变为Base64编码的字符串
// 	var decrypt = CryptoJS.AES.decrypt(str, key, {
// 		mode: CryptoJS.mode.ECB,
// 		spadding: CryptoJS.pad.Pkcs7
// 	})
// 	return decrypt.toString(CryptoJS.enc.Utf8)
// }

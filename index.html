<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<title>代表联络站</title>
	<!-- <link rel="stylesheet" href="./css/index.css"> -->
	<link rel="stylesheet" href="./css/vant.css" />
	<style type="text/css">
		* {
			margin: 0;
			padding: 0;
		}

		.body_box {
			width: 100%;
			height: 100vh;
			background: #fff;
		}

		.homeHead {
			width: 100%;
			height: 190px;
			background-image: url("./img/homeHead.png");
			background-repeat: no-repeat;
			background-size: 100% 100%;
			padding: 20px 16px 0 0;
			box-sizing: border-box;
		}

		.headContBox {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.searchHomebox {
			background: transparent;
			flex: 1;
			display: inline-block;
			padding-left: 20px;
			position: relative;
		}

		.searchHomebox input::-webkit-input-placeholder {
			color: #fff !important;
		}

		.van-button--round {
			height: 28px;
			background: #3a94ff;
			position: absolute;
			right: 6px;
			top: 3px;
		}

		.van-search {
			padding: 0;
			background: transparent;
			width: 100%;
			display: inline-block;
		}

		.van-search__content {
			background: rgba(255, 255, 255, 0.3);
			border-radius: 30px;
		}

		.van-search__content i {
			color: #fff;
		}

		.currentStation {
			color: #fff;
			margin-left: 15px;
		}

		.CityName {
			font-weight: bold;
			margin-right: 3px;
			font-size: 16px;
		}

		.downIcon {
			-webkit-transform: rotate(90deg);
			-moz-transform: rotate(90deg);
			-o-transform: rotate(90deg);
			-ms-transform: rotate(90deg);
			transform: rotate(90deg);
		}

		.van-field__control {
			color: #fff;
		}

		.mainBody {
			height: calc(100% - 184px);
			border-top: 1px solid #fff;
			background: #fff;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
			margin-top: -6px;
			overflow: hidden;
			padding-top: 15px;

		}

		.mainContBox {
			height: 100%;
			overflow: scroll;
		}

		.van-ellipsis {
			font-size: 16px !important;
		}

		.van-tab__text--ellipsis {
			font-size: 16px !important;
		}

		.van-tab {
			color: #999999;
		}

		.van-tab--active {
			color: #333333;
			font-weight: bold;
		}

		.myProposalListBoxHome {
			margin: auto;
			padding: 0 16px;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.dataListStyleHome {
			width: 100%;
			background: #ffffff;
			border-bottom: 0.5px solid #ebebeb;
			border-radius: 0px;
			margin-top: 0px !important;
			padding: 5px 0px !important;
		}

		.dataListBox {
			display: flex;
			align-items: center;
			padding-top: 6px;
		}

		.imgData {
			width: 35%;
			flex-shrink: 0;
			height: 90px;
			padding-left: 2px;
			margin-right: 10px;
			margin-top: 4px;
			margin-bottom: 10px;
			position: relative;
		}

		.headImgStatusStyle {
			position: absolute;
			background-color: #3595ff;
			border-radius: 0px 20px 20px 0px;
			color: #fff;
			font-size: 12px;
			width: 64px;
			height: 20px;
			display: flex;
			align-items: center;
			padding-left: 4px;
			margin: 8px 0;
		}

		.imgUrl {
			border-radius: 4px;
			width: 100%;
			height: 100%;
			object-fit: cover;
			vertical-align: middle;
		}

		.dataList_item_right {
			width: 62%;
			box-sizing: border-box;
			margin: 0 5px;
			margin-right: 10px;
		}

		.title {
			line-height: 23px;
			font-size: 15px;
			font-family: PingFang SC;
			color: #333333;
			overflow: hidden;
			text-overflow: ellipsis;
			margin-bottom: 5px;
			display: flex;
		}

		.flex {
			display: flex;
			align-items: center;
			margin-bottom: 5px;
		}

		.state {
			display: flex;
			align-items: center;
			width: 106px;
			font-size: 12px;
			font-family: PingFang SC;
			color: #666666;
		}

		.img {
			width: 16px;
			height: 16px;
			object-fit: contain;
			vertical-align: middle;
		}

		.name {
			margin-left: 6px;
			line-height: 2;
		}

		.ov {
			width: 80px;
			font-size: 13px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.mobile {
			max-width: 110px;
			font-size: 12px;
			font-family: PingFang SC;
			color: #666666;
			margin-left: 18px;
			overflow: hidden;
			text-overflow: ellipsis;
			/* ellipsis:显示省略符号来代表被修剪的文本  string:使用给定的字符串来代表被修剪的文本*/
			white-space: nowrap;
			/* nowrap:规定段落中的文本不进行换行   */
			overflow: hidden;
		}

		/*超出部分隐藏*/
		.img {
			width: 16px;
			height: 16px;
			object-fit: contain;
			vertical-align: middle;
		}

		.mobilephone {
			vertical-align: middle;
		}

		.name {
			margin-left: 3px;
			line-height: 2;
		}

		.areaName {
			font-size: 12px;
			font-family: PingFang SC;
			color: #666666;
			overflow: hidden;
			text-overflow: ellipsis;
			/* ellipsis:显示省略符号来代表被修剪的文本  string:使用给定的字符串来代表被修剪的文本*/
			white-space: nowrap;
			/* nowrap:规定段落中的文本不进行换行   */
			overflow: hidden;
			/*超出部分隐藏*/
			line-height: 22px;
		}

		.img {
			width: 16px;
			height: 16px;
			object-fit: contain;
			vertical-align: middle;
		}

		.name {
			margin-left: 6px;
			font-size: 13px;
			vertical-align: middle;
		}

		[v-cloak] {
			display: none
		}

		.loading {
			width: 50px;
			height: 50px;
			border: 5px solid #f3f3f3;
			border-top: 5px solid #3498db;
			border-radius: 50%;
			text-align: center;
			animation: spin 1s linear infinite;
			margin: 20px auto;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}
	</style>
</head>

<link>
<div class="body_box" id="app">
	<div class="homeHead">
		<div class="headContBox">
			<div class="searchHomebox">
				<van-search v-model="keyword" placeholder="搜索关键词">
				</van-search>
				<van-button round type="primary" @click="search" v-cloak>搜索</van-button>
			</div>
			<div class="currentStation" @click="chooseCity">
				<span class="CityName" v-cloak>{{citiName}}</span>
				<van-icon class="downIcon" name="play" />
			</div>
		</div>
	</div>
	<div class="mainBody">
		<div class="mainContBox" ref="mainContBox">
			<van-tabs v-model="active" shrink color="#0D75FF" swipeable>
				<van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
					<van-pull-refresh v-model="refreshing" style="min-height: 50vh" @refresh="onRefresh">
						<van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
							offset="52" @load="onLoad">
							<div class="myProposalListBoxHome">
								<div class="loading" v-if="loadingShow"></div>
								<template v-else-if="dataList&&dataList.length!==0">
									<div class="dataListStyleHome" v-for="item in dataList" :key="item.id" @click="openDetails(item)">
										<div class="dataListBox">
											<div class="imgData" v-cloak>
												<div v-if="item.near" class="headImgStatusStyle" v-cloak>离我最近</div>
												<img v-if="item.imgUrl" :src="item.imgUrl" alt="" class="imgUrl" v-cloak />
												<img v-else :src="defaultImg" alt="" class="imgUrl" v-cloak />
											</div>
											<div class="dataList_item_right">
												<div class="title oneLine" v-cloak>
													<img v-cloak src="./img/medal.png" v-if="item.isGiveMedal === '1'" alt=""
														style="width: 18px;">
													<span v-cloak style="margin-left: 5px;"> {{ item.name }}</span>
												</div>
												<div class="flex">
													<div class="state">
														<img v-cloak src="./img/people.png" alt="" class="img" />
														<span v-cloak class="name ov oneLine">{{
															item.contacts
															}}</span>
													</div>
													<div class="mobile" v-if="item.mobile" v-cloak>
														<img src="./img/phone-telephone.png" alt="" class="img" />
														<span class="mobilephone">{{ item.mobile }}</span>
													</div>
												</div>
												<div class="areaName" v-cloak>
													<img src="./img/local.png" alt="" class="img" />
													<span class="name">{{ item.address }}</span>
												</div>
											</div>
										</div>
									</div>
								</template>
							</div>
						</van-list>
					</van-pull-refresh>
				</van-tab>
			</van-tabs>
		</div>
	</div>
</div>
<script src="./js/vue.min.js"></script>
<script src="./js/vant.min.js"></script>
<script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
<script defer type="text/javascript" src="./js/aes.js"></script>
<script type="text/javascript" src="./js/userinfo.js"></script>
<script src="./js/SM.js" type="text/javascript" charset="utf-8"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>

<script>
	var allListKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 获取所有站点私钥
	var publicAllListKey = '04e68f60ab0efc9c89cb966d6dd43f1bd6de9a47ee19395c84a00c87cfc8af135466b4b97fdb373f23db9c72d90a14f3cfc2d6aaf8f67e0dffab36dad384cf2319' 	// 获取所有站点公钥
	var queryObj = {}
	var app = new Vue({
		el: "#app",
		data: {
			keyword: '',
			citiName: '青岛市',
			regionId: '',
			streetId: '',
			active: '0',
			activeData: [
				{ id: '0', value: '所有站点' }
				// { id: '1', value: '关注站点' },
				// { id: '2', value: '我的站点' }
			],
			refreshing: false,
			loading: false,
			finished: false,
			dataList: [],
			defaultImg: './img/bannerRD.png',
			loadingShow: true,
			flag: true,

			pageNo: 1,
			pageSize: 15,
			areaListData: [],
			headTheme: '#3657C0',
			scrollTop: 0,
			area: { key: "430000", value: "" },
			areas: {},
			showCustomPopup: false,
			textDialog: ''
		},
		mounted () {
			const urlParams = new URLSearchParams(window.location.search);
			queryObj = JSON.parse(urlParams.get('queryObj'))
			if (queryObj) {
				this.citiName = queryObj.CityName || '青岛市'
				if (queryObj.street) {
					this.streetId = queryObj.street
					this.getAllList()
				} else {
					this.regionId = queryObj.district
					this.getAllList()
				}
			} else {
				this.getAllList()
			}
		},
		methods: {

			// 搜索
			search () {
				this.pageNo = 1
				this.dataList = []
				this.finished = false
				this.getAllList()
			},
			// 选择地区
			chooseCity () {
				window.location.href = './view/chooseCity.html'
			},
			// 刷新
			onRefresh () {
				this.pageNo = 1
				this.dataList = []
				this.finished = false
				this.getAllList()
			},
			// 下拉加载
			onLoad () {
				if (this.flag) {
					this.getAllList()
				}
			},
			// 获取所有站点列表
			getAllList () {
				var that = this
				that.flag = false
				// that.dataList = []
				let headers = {
					"U-Login-Areaid": '370200'
				}
				var interfacecontent = { "pageNo": that.pageNo, "pageSize": that.pageSize, "keyword": that.keyword, "district": this.regionId, "street": this.streetId }
				let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListKey)
				let fdDqtq = new FormData()
				fdDqtq.append('app_id', 'dbllztuhek')
				fdDqtq.append('interface_id', 'qdrdllzAllSiteList')
				fdDqtq.append('version', '1.0')
				fdDqtq.append('biz_content', biz_content)
				fdDqtq.append('charset', 'utf-8')
				fdDqtq.append('timestamp', (new Date()).valueOf())
				fdDqtq.append('origin', '1')
				fdDqtq.append('sign', 'signResult')
				fdDqtq.append('header', JSON.stringify(headers))
				$.ajax({
					url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
					type: 'post',
					dataType: 'json',
					data: fdDqtq,
					contentType: false,
					processData: false,
					cache: false,
					success: function (data) {
						var ret = JSON.parse(SM.decrypt(data.data, allListKey))
						console.log('ret==>>>', ret)
						var { total } = ret
						if (that.pageNo === 1) {
							ret.data.forEach(item => {
								if (item.imgUrl) {
									item.imgUrl = item.imgUrl.replace("http://120.221.72.187:81", "https://szrd.qingdao.gov.cn:8089");
								}
							})
							that.dataList = ret.data || []
						} else {
							ret.data.forEach(item => {
								if (item.imgUrl) {
									item.imgUrl = item.imgUrl.replace("http://120.221.72.187:81", "https://szrd.qingdao.gov.cn:8089");
								}
							})
							that.dataList = that.dataList.concat(ret.data)
						}
						that.loadingShow = false
						that.pageNo = that.pageNo + 1
						that.loading = false
						that.refreshing = false
						that.flag = true
						// 数据全部加载完成
						if (that.dataList.length >= total) {
							that.finished = true
						}
					},
					error: function (data) {
						console.log('请求失败了！', data)
					}
				})
			},
			// 手机号脱敏
			maskMobile (mobile) {
				return mobile.slice(0, 3) + "*".repeat(6) + mobile.slice(-2);
			},
			// 进详情
			openDetails (_item) {
				window.location.href = './view/contactStationInfo.html?id=' + _item.id
			},
		}
	})

	window.onscroll = function () {
		var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
		app.scrollTop = scrollTop
	}
</script>

</body>

</html>
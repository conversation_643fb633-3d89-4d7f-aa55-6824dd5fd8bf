<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>济事商量</title>
  <style type="text/css">
    * {
      margin: 0;
      padding: 0;
    }

    body {
      background: #fff;
    }

    .body_box {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .limitRows {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .center-top {
      padding: 20px;
      border-bottom: 1px solid rgba(153, 153, 153, 0.2);
    }

    .title {
      height: 22px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .name {
      min-height: 20px;
      max-height: 40px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }

    .center-cen {
      padding: 20px;
    }

    .message_title {
      height: 22px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
    }

    .message {
      margin-top: 5px;
      width: 100%;
      min-height: 60px;
      background: #f8f9fa;
      border: 1px solid #eff2f5;
      border-radius: 4px;
    }

    .msg {
      padding: 10px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #666666;
      width: 100%;
      height: auto;
      word-wrap: break-word;
      word-break: break-all;
      overflow: hidden;
    }

    .imgList {
      padding: 0 10px 10px 10px;
    }

    .imgList img {
      width: 60px;
      height: 45px;
      vertical-align: middle;
      margin-right: 5px;
    }

    .ReplyInfoClass {
      margin-top: 20px;
    }

    .title-bot {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #999999;

    }

    .title-bot .name {
      margin-right: 5px;
    }

    .enclosure {
      padding: 5px 10px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #999999;

    }

    .enclosure .names {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #0d75ff;
    }

    [v-cloak] {
      display: none
    }

    .nodata {
      margin-top: 30px;
      text-align: center;
      color: #ccc;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <div class="center-top">
      <div class="title" v-cloak>{{details.title}}</div>
      <div class="name" v-cloak>反映人：{{details.name}}</div>
      <div class="name" v-cloak>联系方式：{{details.mobile}}</div>
      <div class="name" v-cloak>留言时间：{{details.reflectDate}}</div>
      <div class="name limitRows" v-cloak>归属站点：{{details.studioName}}</div>
    </div>
    <div class="center-cen">
      <div class="message_title">留言内容</div>
      <div class="message">
        <div class="msg" v-cloak>
          {{ details.leaveMessage }}
        </div>
        <div class="imgList">
          <img v-for="(item,index) in details.leaveMessageImg " :key="index" :src="item.fileUrl" alt="">
        </div>
      </div>
    </div>
    <template v-if="details.replyContentListVos">
      <div class="center-cen" v-show="details.replyContentListVos.length">
        <div class="title">回复情况</div>
        <div v-for="(item,id) in details.replyContentListVos" :key="id" class="ReplyInfoClass">
          <div class="title-bot" v-cloak><span
              class="name">{{item.replyFlag==='1'?'回复人：':'反映人：'}}{{item.name}}</span><span>{{item.replyDate}}</span>
          </div>
          <div class="message">
            <div class="msg" v-cloak>
              {{item.content}}
            </div>
            <div class="imgList" v-if="item.imgUrlList">
              <img v-for="(childItem,index) in item.imgUrlList " :key="index" :src="childItem.fileUrl" alt=""
                @click="viewImg(item.imgUrlList,index)">
            </div>
            <template v-if="item.docUrlList">
              <div class="enclosure" v-if="item.docUrlList.length>0">附件：
                <div class="names" v-for="(fileItem,index) in item.docUrlList " :key="index">
                  <a :href="fileItem.fileUrl" v-cloak>{{fileItem.fileName}}</a>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
  <script>
    var massMessagesInfoKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' //私钥
    var publicMassMessagesInfoKey = '04641be3f88f786d0f4df4a1e51be688183592c02c2809eb24ec4e32a038a0d86cee94db6dc57b4c399641daed8befa9499a4d4fa76de4631797dd243ec049d415' 	// 为民办事公钥
    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        details: {}
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id')
        if (id) {
          this.getInfo()
        }
      },
      methods: {
        getInfo () {
          var that = this
          let headers = {
            "U-Login-Areaid": '370200'
          }
          var interfacecontent = { "id": id }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicMassMessagesInfoKey)
          let fdDqtq = new FormData()
          fdDqtq.append('app_id', 'dbllztuhek')
          fdDqtq.append('interface_id', 'qdrdllzMassMessagesInfo')
          fdDqtq.append('version', '1.0')
          fdDqtq.append('biz_content', biz_content)
          fdDqtq.append('charset', 'utf-8')
          fdDqtq.append('timestamp', (new Date()).valueOf())
          fdDqtq.append('origin', '1')
          fdDqtq.append('sign', 'signResult')
          fdDqtq.append('header', JSON.stringify(headers))
          $.ajax({
            url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
            type: 'post',
            dataType: 'json',
            data: fdDqtq,
            contentType: false,
            processData: false,
            cache: false,
            success: function (data) {
              try {
                var ret = JSON.parse(SM.decrypt(data.data, massMessagesInfoKey))
                that.details = ret.data
              } catch (error) {
                // 在这里处理异常情况
                console.log('解析JSON时发生错误：', error);
                that.getInfo()
              }
            },
            error: function (data) {
              console.log('请求失败了！', data)
            }
          })
        },
      }
    })
  </script>
</body>

</html>
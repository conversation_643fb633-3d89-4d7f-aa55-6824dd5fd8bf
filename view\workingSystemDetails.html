<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>代表联络站</title>
    <link rel="stylesheet" href="../css/vant.css" />
    <style type="text/css">
      * {
        margin: 0;
        padding: 0;
      }

      body {
        background: #fff;
      }

      .body_box {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .center {
        padding: 16px;
      }

      .title {
        width: 100%;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 600;
        line-height: 37px;
        color: #333333;
      }

      .pushDate {
        margin: 1.75vw 0 3vw 0;
        font-size: 3.5vw;
        color: #999;
      }

      .text {
        width: 100%;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #666666;
      }

      .contentStyle {
        width: 100%;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #666666;
      }

      .contentStyle img {
        width: 350px !important;
        height: auto !important;
        vertical-align: middle;
        margin: 10px auto !important;
        display: flex;
        justify-content: center;
      }

      .contentStyle p {
        font-size: 16px !important;
        background-color: #fff !important;
        /* width: 360px !important; */
      }

      [v-cloak] {
        display: none;
      }

      .nodata {
        margin-top: 30px;
        text-align: center;
        color: #ccc;
      }
    </style>
  </head>

  <body>
    <div class="body_box" id="app">
      <div class="center">
        <div class="title" v-cloak>{{details.name}}</div>
        <div class="pushDate" v-cloak>{{details.pushDate}}</div>
        <div v-html="details.content" class="contentStyle" v-cloak></div>
      </div>
    </div>
    <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
    <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
    <script defer type="text/javascript" src="../js/aes.js"></script>
    <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/vant.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
    <script>
      var privateKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
      var publicWorkingSystemInfoKey = '047df480011b029b9a1ecc435c8875a6f9a2c0a0c7a0f79da0b3c25722f2caa61a802586dbc10c2fb86b1e63ec30a9c029d8e086314bf250438a0a872de8852734' // 获取工作制度详情公钥
      var id = ''
      var app = new Vue({
        el: '#app',
        data: {
          details: {}
        },
        async mounted() {
          // var vConsole = new VConsole() // 初始化
          const urlParams = new URLSearchParams(window.location.search)
          id = urlParams.get('id')
          if (id) {
            this.getInfo()
          }
        },
        methods: {
          getInfo() {
            var that = this
            let headers = {
              'U-Login-Areaid': '370200'
            }
            var interfacecontent = { id: id }
            let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicWorkingSystemInfoKey)
            let fdDqtq = new FormData()
            fdDqtq.append('app_id', 'dbllztuhek')
            fdDqtq.append('interface_id', 'qdrdllzWorkRegimeInfo')
            fdDqtq.append('version', '1.0')
            fdDqtq.append('biz_content', biz_content)
            fdDqtq.append('charset', 'utf-8')
            fdDqtq.append('timestamp', new Date().valueOf())
            fdDqtq.append('origin', '1')
            fdDqtq.append('sign', 'signResult')
            fdDqtq.append('header', JSON.stringify(headers))
            $.ajax({
              url: 'https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway',
              type: 'post',
              dataType: 'json',
              data: fdDqtq,
              contentType: false,
              processData: false,
              cache: false,
              success: function (data) {
                try {
                  var ret = JSON.parse(SM.decrypt(data.data, privateKey))
                  console.log('详情：', ret)
                  that.details = ret.data
                } catch (error) {
                  // 在这里处理异常情况
                  console.log('解析JSON时发生错误：', error)
                  that.getInfo()
                }
              },
              error: function (data) {
                console.log('请求失败了！', data)
              }
            })
          }
        }
      })
    </script>
  </body>
</html>

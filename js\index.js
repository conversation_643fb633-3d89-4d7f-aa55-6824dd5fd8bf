$(function(){
	$('.back').click(function(){history.go(-1);});
	$('.add-btn').click(function(){window.location.href = "yueduhuiyiAdd.html";});
	/* 发言 按钮 */
	$('.fayan-btn').click(function(e) {$('.fayan-btn').hide();$('#fayan').show();e.preventDefault();e.stopPropagation();});
	$('body').on('click',function(e){if(e.target.id == ''){$('.fayan-btn').show();$('#fayan').hide();}})
	$('.huifu-btn').click(function(e){
		$('#fayan').show();
		e.preventDefault();
		e.stopPropagation();
	})
	/* 月度会议 */
	if($('.zhuti').height() >= 100){
		$('#zhankai').css("display","block");$(".zhuti").addClass("neirong");$(".detail-btn").text("展开详细");
		$("#zhankai").click(function(e){$(".zhuti").toggleClass("neirong");if($(".detail-btn").text() == "展开详细"){$(".detail-btn").text("收起");}else if($(".detail-btn").text() == "收起"){$(".detail-btn").text("展开详细");}e.preventDefault(); e.stopPropagation();});
	}
	/* 评论 展开收起 */
	// $(".msg").each(function(index,element){
	// 	if($(this).height() >= 40){
	// 		$(this).addClass("neirong");$(this).next().css("display", "block");$(this).next().text("[展开]");
	// 		$(this).next().on('click', function(e) {$(this).prev().toggleClass("neirong");if ($(this).text() == "[展开]") {$(this).text("[收起]");} else if ($(this).text() == "[收起]") {$(this).text("[展开]");}e.preventDefault();e.stopPropagation();});
	// 	}
	// });
})
/* 时间初始化 */
function getDate(str){
	var thisDate = new Date(str);
	var year = thisDate.getFullYear();
	var month = thisDate.getMonth() + 1 < 10 ? '0' + (thisDate.getMonth() + 1) : thisDate.getMonth() + 1;
	var day = thisDate.getDate() < 10 ? '0' + thisDate.getDate() : thisDate.getDate();
	var hour = thisDate.getHours() < 10 ? '0' + thisDate.getHours() : thisDate.getHours();
	var min = thisDate.getMinutes() < 10 ? '0' + thisDate.getMinutes() : thisDate.getMinutes();
	var seconds = thisDate.getSeconds() < 10 ? '0' + thisDate.getSeconds() : thisDate.getSeconds();
	var time = year + '-' + month + '-' + day + ' ' + hour + ':' + min;
	return time;
}

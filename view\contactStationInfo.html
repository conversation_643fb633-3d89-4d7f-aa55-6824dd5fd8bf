<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>代表联络站</title>
  <!-- <link rel="stylesheet" href="./css/index.css"> -->
  <link rel="stylesheet" href="../css/vant.css" />
  <style type="text/css">
    * {
      margin: 0;
      padding: 0;
    }

    body {
      background: #f8f8f8;
    }

    .body_box {
      width: 100%;
      height: 100vh;
    }

    [v-cloak] {
      display: none
    }

    .proposalInfoBox {
      width: 100%;
      background-color: #f4f6f8;
      margin-bottom: 2.5vw;
    }

    .banner {
      padding: 15px;
    }

    .banner .img {
      width: 100%;
      height: 160px;
      border-radius: 10px;
      max-height: 45vw;
    }

    .flex {
      position: relative;
      padding: 0 4vw 4vw 4vw;
      display: flex;
      align-items: center;
      border-radius: 2.5vw 2.5vw 0 0;
      background-color: #fff;
      margin: 0 3.75vw;
    }

    .left {
      width: 80%;
    }

    .relative {
      position: relative;
    }

    .absolute {
      width: 86px;
      position: absolute;
      left: 0;
      top: 15px;
      z-index: 2;
    }

    .absoluteButtom {
      position: absolute;
      left: 0;
      top: 26px;
      z-index: 1;
    }

    .proposalTitle {
      font-size: 16px;
      font-family: PingFang SC;
      line-height: 24px;
      color: #333333;
      margin-top: 48px;
      display: flex;
    }

    .proposalInfo {
      width: 100%;
      padding: 1.5vw 0;
    }

    .proposalInfoLeft {
      display: flex;
    }

    .state {
      font-size: 14px;
      font-weight: 400;
      line-height: 5.5vw;
      color: #666;
    }

    .contacts {
      font-size: 14px;
      color: #333333;
      margin-top: 5px;
    }

    .contactsName {
      margin-right: 10px;
      color: #999999;
    }

    .telephones {
      font-size: 14px;
      color: #333333;
      margin-top: 10px;
    }

    .phone-telephone {
      margin-right: 10px;
      color: #999999;
    }

    .areaName {
      font-size: 14px;
      font-weight: 400;
      line-height: 5.5vw;
      color: #666;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
      margin-top: 10px;
    }

    .marginRight {
      width: 3.75vw;
      vertical-align: middle;
      margin-bottom: 3px;
    }

    .qrCode {
      margin: 0 15px;
      background-color: #fff;
      border-radius: 0px 0 10px 10px;
      text-align: center;
      height: 40px;
      line-height: 40px;
    }

    .qrCodeFs {
      width: 324px;
      margin: 0 auto;
      height: 0;
      border-top: 0.5px solid #ebebeb;
    }

    .img {
      width: 16px;
      height: 16px;
      vertical-align: middle;
    }

    .qrCodeName {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #3894ff;
      margin-left: 5px;
    }

    .mgBottom {
      margin: 0 15px;
    }

    .proposalProcessBox {
      width: 100%;
      padding: 16px 0 0;
      background-color: #fff;
      border-radius: 10px;
    }

    .proposalText {
      padding: 0 16px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 20px;
      color: #333333;
      margin-bottom: 10px;
    }

    .dbRelative {
      position: relative;
      margin: 35px 0;
    }

    .dbAbsolute {
      width: 67px;
      position: absolute;
      top: -35px;
      bottom: 0px;
      z-index: 2;
    }

    .dbAbsoluteButtom {
      position: absolute;
      top: -23px;
      bottom: 0px;
      z-index: 1;
    }

    .proposalProcess {
      width: 100%;
      display: flex;
      overflow-y: hidden;
    }

    .proposalProcessItem {
      height: 140px;
      margin-left: 16.5px;
      margin-bottom: 16px;
      border-radius: 5px;
      border: 1px #eff2f5 solid;
    }

    .processIcon {
      width: 74px;
      height: 84px;
      text-align: center;
      z-index: 2;
    }

    .processIcon .img {
      width: 100%;
      height: 100%;
      border-radius: 4px 4px 0px 0px;
      vertical-align: middle;
    }

    .name {
      width: 100%;
      display: block;
      margin-top: 5px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #000000;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      text-overflow: ellipsis;
      /* ellipsis:显示省略符号来代表被修剪的文本  string:使用给定的字符串来代表被修剪的文本*/
      white-space: nowrap;
      /* nowrap:规定段落中的文本不进行换行   */
      overflow: hidden;
      /*超出部分隐藏*/
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .position {
      width: 100%;
      display: block;
      margin: 2px 0;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .jjRelative {
      position: relative;
      margin: 30px 15px;
    }

    .jjAbsolute {
      width: 86px;
      position: absolute;
      top: -25px;
      bottom: 0;
      z-index: 1;
    }

    .jjAbsoluteButtom {
      top: -12px;
      bottom: 0;
      position: absolute;
      z-index: 2;
    }

    .abstract {
      border-bottom: 0.5px solid #ebebeb;
      margin: 0 10px;
    }

    .introduce {
      font-size: 15px;
      padding: 16px;
      font-family: PingFang SC-Medium;
      line-height: 24px;
      color: #333333;
    }

    .dataListBox {
      display: flex;
      align-items: center;
      padding-top: 1.5vw;
      border-bottom: 1px solid hsla(0, 0%, 60%, .2);
      padding: 2.5vw 3vw;
    }

    .img4 {
      width: 35%;
    }

    .img4 .img {
      width: 25vw;
      height: 17.75vw;
      border-radius: 1vw;
    }

    .item-right {
      width: 65%;
      height: 100%;
    }

    .workingSystem_title {
      width: 100%;
      font-size: 15px;
      /* font-weight: 600; */
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }

    .workingSystem_timeDate {
      margin-top: 6.25vw;
      display: flex;
      justify-content: flex-end;
      height: 5vw;
      font-size: 14px;
      font-weight: 400;
      color: #666;
    }

    .item-rights {
      width: 100%;
    }

    .title {
      display: flex;
      justify-content: space-between;
    }

    .title-left {
      width: 90%;
      height: 5vw;
      font-size: 15px;
      color: #333;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      margin-left: 5vw;
      margin-top: 1vw;
    }

    .title-right {
      width: 15%;
      font-size: 14px;
      font-weight: 400;
      color: #666;
      background: hsla(0, 0%, 60%, .1);
      padding: 0.75vw;
    }

    .title-rights {
      width: 15%;
      font-size: 14px;
      font-weight: 400;
      color: #3894ff;
      background: rgba(56, 148, 255, .1);
      padding: 0.75vw;
    }

    .areaNames {
      display: flex;
      justify-content: space-between;
      margin-top: 4.125vw;
      white-space: nowrap;
    }

    .name {
      margin-top: 2.5vw;
      font-size: 14px;
      font-weight: 400;
      color: #666;
    }

    .name .img {
      width: 3.75vw;
      margin-bottom: -0.5vw;
    }

    .names {
      margin-top: 2.5vw;
      font-size: 14px;
      font-weight: 400;
      color: #666;
    }

    .names .img {
      width: 3.75vw;
      margin-bottom: -0.5vw;
    }

    .loading {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      text-align: center;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
    }
  </style>
</head>

<link>
<div class="body_box" id="app">
  <div class="proposalInfoBox">
    <div class="banner">
      <img :src="details.imgUrl?details.imgUrl:'../img/bannerRD.png'" alt="" class="img" v-cloak>
      <!-- <img src="../img/bannerRD.png" alt="" class="img"> -->
    </div>
    <div class="flex">
      <div class="left">
        <div class="relative">
          <img class="absolute" src="../img/information.png" alt="" />
          <img class="absoluteButtom" src="../img/Rectangle.png" alt="" />
        </div>
        <div class="proposalTitle" v-cloak><span v-cloak>{{details.name}}</span></div>
        <div class="proposalInfo">
          <div class="proposalInfoLeft">
            <div class="state oneLine">
              <div class="contacts" v-cloak><span class="contactsName">联系人</span>{{details.contacts}}</div>
              <!-- <div v-if="details.mobile" class="telephones"><span class="phone-telephone">电话</span>{{details.mobile}}
              </div> -->
            </div>
          </div>
          <div class="proposalInfoLeft" v-cloak>
            <div class="areaName">
              <img class="marginRight" src="../img/locals.png" alt="" v-cloak />
              <span v-cloak>{{details.address}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="qrCode" @click="isQrCode(details)">
      <div class="qrCodeFs"></div>
      <img src="../img/qrCode.png" alt="" class="img" />
      <span class="qrCodeName">我要留言</span>
    </div>
  </div>
  <div class="mgBottom">
    <div class="proposalProcessBox proposalInfoBox">
      <div class="proposalText">
        <div class="dbRelative">
          <img class="dbAbsolute" src="../img/representative.png" alt="" />
          <img class="dbAbsoluteButtom" src="../img/Rectangle.png" alt="" />
        </div>
      </div>
      <div class="proposalProcess">
        <div class="loading" v-if="loadingShow"></div>
        <template v-else-if="memberOfTheWallDetails&&memberOfTheWallDetails.length!==0">
          <div class="proposalProcessItem" v-for="(item,index) in memberOfTheWallDetails" :key="index" v-cloak>
            <div class="processIcon" @click="toCommitteeDetails(item)">
              <img v-if="item.headImg" :src="item.headImg" alt="" class="img" />
              <img v-else src="../img/def_head_img.jpg" alt="" class="img" />
              <span class="name">{{ item.userName }}</span>
              <span class="position">{{ item.areaTypeName }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
  <div class="mgBottom">
    <div class="proposalProcessBox">
      <div class="jjRelative">
        <img class="jjAbsolute" src="../img/particulars.png" alt="" />
        <img class="jjAbsoluteButtom" src="../img/Rectangle.png" alt="" />
      </div>
      <van-tabs v-model="active" shrink @click="onClickTab" color="#3894FF" title-active-color="#333333"
        title-inactive-color="#999999">
        <van-tab title="简介">
          <div class="abstract"></div>
          <div class="introduce" v-cloak>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{details.introduce}}
          </div>
        </van-tab>
        <van-tab title="活动动态" v-cloak>
          <div class="loading" v-if="loadingShow1"></div>
          <template v-else-if="activityDynamicsData&&activityDynamicsData.length!==0">
            <div class="dataListBox" v-for="item in activityDynamicsData" @click="openActivityDynamicsDetails(item)">
              <div class="img4">
                <img :src="item.imgPath?item.imgPath:'../img/gpx_work2.png'" alt="" class="img">
              </div>
              <div class="item-right">
                <div class="workingSystem_title">{{item.name}}</div>
                <div class="workingSystem_timeDate">{{item.createDate}}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="为民办事" v-cloak>
          <div class="loading" v-if="loadingShow3"></div>
          <template v-else-if="massMessagesData&&massMessagesData.length!==0">
            <div class="dataListBox" v-for="item in massMessagesData" @click="openMassMessagesDetails(item)">
              <div class="item-rights">
                <div class="title">
                  <div :class="item.status === '0' ? 'title-right': 'title-rights'">
                    {{item.status === '0' ? '未回复':'已回复' }}</div>
                  <div class="title-left">{{item.title}}</div>
                </div>
                <div class="areaNames">
                  <div class="name">
                    <img src="../img/peoples.png" alt="" class="img" />
                    {{item.name}}
                  </div>
                  <div class="names">
                    <img src="../img/phone-telephones.png" alt="" class="img" />
                    {{item.mobile}}
                  </div>
                  <div class="names">
                    <img src="../img/alarm-clock.png" alt="" class="img" />
                    {{item.updateDate.substr(0,10)}}
                  </div>
                  <!-- <div class="name" v-cloak>留言人：{{item.name}}</div>
                  <div class="name" v-cloak>电话：{{item.mobile}}</div>
                  <div class="name" v-cloak>留言时间：{{item.updateDate}}</div> -->
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="工作制度" v-cloak>
          <div class="loading" v-if="loadingShow2"></div>
          <template v-else-if="workingSystemData&&workingSystemData.length!==0">
            <div class="dataListBox" v-for="item in workingSystemData" @click="openWorkingSystemDetails(item)">
              <div class="img4">
                <img :src="item.img?item.img:'../img/gpx_work.png'" alt="" class="img">
              </div>
              <div class="item-right">
                <div class="workingSystem_title">{{item.name}}</div>
                <div class="workingSystem_timeDate">{{item.createDate}}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</div>
<script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
<script defer type="text/javascript" src="../js/aes.js"></script>
<script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
<script src="../js/vue.min.js"></script>
<script src="../js/vant.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>

<script>
  var privateKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
  var publicAllListInfoKey = '04fea396d72957239e4af46b870d02bb721e10192c4528f36c519fad68d9193bd312db5f13abbd368afc939e50ae7dd639ac599b07d533e8d6c4d300f0ca958012' 	// 获取所有站点详情公钥
  var publicResidentStationKey = '04f490a3729b54aefaaaa773407f6468f7e1eff5459508a09b7dd7ab53035361f3df67724b81d55d9160fc42ae172bd3922dec4fee23fd8042a2a54d675f506185' 	// 获取驻站代表公钥
  var publicActivityDynamicListKey = '04d77627231aa3e5989e74832d7b6e05425f9ea4bb407f31e6a4b214357e783a5b0a624a54774b4035e27c81d8f66c8ed7b5483920800759fc923ea43f071d4e4c' 	// 获取活动动态公钥
  var publicWorkingSystemKey = '0442464fd04299814b97ae8771298397cd8f346447ed373569b5a408e24fdadfb8f7345a10e0c5238a204078e0dfabb577c38df17366ca47dd8a70413762c65c40' 	// 获取工作制度公钥
  var publicMassMessagesKey = '045f1dedd3e6e59b26cbc38e3465b8e1b091b4b26029b7703ef2b65536e7335e46a07d3b9562b40d9cf67acf33895848c3d3ecfb8ab594cac3c9662fd5929d2e89' 	// 获取为民办事公钥
  var id = ''
  var app = new Vue({
    el: "#app",
    data: {
      details: {},
      memberOfTheWallDetails: [],
      loadingShow: true,
      loadingShow1: true,
      loadingShow2: true,
      loadingShow3: true,
      active: 0,
      activityDynamicsData: [],
      massMessagesData: [],
      workingSystemData: []
    },
    mounted () {
      const urlParams = new URLSearchParams(window.location.search);
      id = urlParams.get('id')
      if (id) {
        this.getInfo()
        // this.getResidentStation()
      }
    },
    methods: {
      // 获取详情
      getInfo () {
        var that = this
        let headers = {
          "U-Login-Areaid": '370200'
        }
        var interfacecontent = { "id": id }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListInfoKey)
        let fdDqtq = new FormData()
        fdDqtq.append('app_id', 'dbllztuhek')
        fdDqtq.append('interface_id', 'qdrdllzInfo')
        fdDqtq.append('version', '1.0')
        fdDqtq.append('biz_content', biz_content)
        fdDqtq.append('charset', 'utf-8')
        fdDqtq.append('timestamp', (new Date()).valueOf())
        fdDqtq.append('origin', '1')
        fdDqtq.append('sign', 'signResult')
        fdDqtq.append('header', JSON.stringify(headers))
        $.ajax({
          url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
          type: 'post',
          dataType: 'json',
          data: fdDqtq,
          contentType: false,
          processData: false,
          cache: false,
          success: function (data) {
            try {
              var ret = JSON.parse(SM.decrypt(data.data, privateKey))
              console.log('获取详情==>>>', ret)
              if (ret.errcode == 200) {
                that.details = ret.data
                that.getResidentStation()
              } else {
                that.details = {}
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error);
              that.getInfo()
            }
          },
          error: function (data) {
            console.log('请求失败了！', data)
          }
        })
      },
      //  获取驻站代表
      getResidentStation () {
        var that = this
        let headers = {
          "U-Login-Areaid": '370200'
        }
        var interfacecontent = { "studioId": id, "pageNo": 1, "pageSize": 999 }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicResidentStationKey)
        let fdDqtq = new FormData()
        fdDqtq.append('app_id', 'dbllztuhek')
        fdDqtq.append('interface_id', 'qdrdllzResidentList')
        fdDqtq.append('version', '1.0')
        fdDqtq.append('biz_content', biz_content)
        fdDqtq.append('charset', 'utf-8')
        fdDqtq.append('timestamp', (new Date()).valueOf())
        fdDqtq.append('origin', '1')
        fdDqtq.append('sign', 'signResult')
        fdDqtq.append('header', JSON.stringify(headers))
        $.ajax({
          url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
          type: 'post',
          dataType: 'json',
          data: fdDqtq,
          contentType: false,
          processData: false,
          cache: false,
          success: function (data) {
            try {
              var ret = JSON.parse(SM.decrypt(data.data, privateKey))
              console.log('获取驻站代表==>>>', ret)
              if (ret.errcode == 200) {
                that.memberOfTheWallDetails = ret.data
                that.loadingShow = false
              } else {
                that.memberOfTheWallDetails = []
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error);
              that.getResidentStation()
            }
          },
          error: function (data) {
            console.log('请求失败了！', data)
          }
        })
      },
      // 获取活动动态列表
      getActivityDynamicList () {
        var that = this
        let headers = {
          "U-Login-Areaid": '370200'
        }
        var interfacecontent = { "studioId": id, "pageNo": 1, "pageSize": 100 }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicActivityDynamicListKey)
        let fdDqtq = new FormData()
        fdDqtq.append('app_id', 'dbllztuhek')
        fdDqtq.append('interface_id', 'qdrdllzActivityList')
        fdDqtq.append('version', '1.0')
        fdDqtq.append('biz_content', biz_content)
        fdDqtq.append('charset', 'utf-8')
        fdDqtq.append('timestamp', (new Date()).valueOf())
        fdDqtq.append('origin', '1')
        fdDqtq.append('sign', 'signResult')
        fdDqtq.append('header', JSON.stringify(headers))
        $.ajax({
          url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
          type: 'post',
          dataType: 'json',
          data: fdDqtq,
          contentType: false,
          processData: false,
          cache: false,
          success: function (data) {
            try {
              var ret = JSON.parse(SM.decrypt(data.data, privateKey))
              console.log('获取活动动态==>>', ret)
              that.activityDynamicsData = ret.data
              that.loadingShow1 = false
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error);
              that.getActivityDynamicList()
            }
          },
          error: function (data) {
            console.log('请求失败了！', data)
          }
        })
      },
      // 获取工作制度列表
      getWorkingSystemList () {
        var that = this
        let headers = {
          "U-Login-Areaid": '370200'
        }
        var interfacecontent = { "studioId": id, "pageNo": 1, "pageSize": 100 }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicWorkingSystemKey)
        let fdDqtq = new FormData()
        fdDqtq.append('app_id', 'dbllztuhek')
        fdDqtq.append('interface_id', 'qdrdllzWorksystemList')
        fdDqtq.append('version', '1.0')
        fdDqtq.append('biz_content', biz_content)
        fdDqtq.append('charset', 'utf-8')
        fdDqtq.append('timestamp', (new Date()).valueOf())
        fdDqtq.append('origin', '1')
        fdDqtq.append('sign', 'signResult')
        fdDqtq.append('header', JSON.stringify(headers))
        $.ajax({
          url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
          type: 'post',
          dataType: 'json',
          data: fdDqtq,
          contentType: false,
          processData: false,
          cache: false,
          success: function (data) {
            try {
              var ret = JSON.parse(SM.decrypt(data.data, privateKey))
              console.log('获取工作制度列表==>', ret)
              that.workingSystemData = ret.data
              that.loadingShow2 = false
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error);
              that.getWorkingSystemList()
            }
          },
          error: function (data) {
            console.log('请求失败了！', data)
          }
        })
      },
      // 获取为民办事列表
      getMassMessagesList () {
        var that = this
        let headers = {
          "U-Login-Areaid": '370200'
        }
        var interfacecontent = { "studioId": id, "pageNo": 1, "pageSize": 100, "areaId": localStorage.getItem('areaId') }
        console.log('interfacecontent===>', interfacecontent)
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicMassMessagesKey)
        let fdDqtq = new FormData()
        fdDqtq.append('app_id', 'dbllztuhek')
        fdDqtq.append('interface_id', 'qdrdllzServingList')
        fdDqtq.append('version', '1.0')
        fdDqtq.append('biz_content', biz_content)
        fdDqtq.append('charset', 'utf-8')
        fdDqtq.append('timestamp', (new Date()).valueOf())
        fdDqtq.append('origin', '1')
        fdDqtq.append('sign', 'signResult')
        fdDqtq.append('header', JSON.stringify(headers))
        $.ajax({
          url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
          type: 'post',
          dataType: 'json',
          data: fdDqtq,
          contentType: false,
          processData: false,
          cache: false,
          success: function (data) {
            try {
              var ret = JSON.parse(SM.decrypt(data.data, privateKey))
              console.log('获取为民办事列表==>', ret)
              that.massMessagesData = ret.data
              that.loadingShow3 = false
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error);
              that.getMassMessagesList()
            }
          },
          error: function (data) {
            console.log('请求失败了！', data)
          }
        })
      },
      // 跳转留言
      isQrCode () {
        window.location.href = './submitMessages.html?title=' + this.details.name + '&studioId=' + id
      },
      // 跳转代表详情
      toCommitteeDetails (_item) {
        window.location.href = './residentRepresentativeInfo.html?userId=' + _item.userId + '&studioId=' + _item.studioId
      },
      // tab切换
      onClickTab (name, title) {
        if (title == '为民办事') {
          this.getMassMessagesList()
        } else if (title == '工作制度') {
          this.getWorkingSystemList()
        } else {
          this.getActivityDynamicList()
        }
      },
      // 进活动动态详情
      openActivityDynamicsDetails (_item) {
        window.location.href = './activityDynamicsDetails.html?id=' + _item.id
      },
      // 进为民办事详情
      openMassMessagesDetails (_item) {
        window.location.href = './massMessagesDetails.html?id=' + _item.id
      },
      // 进工作制度详情
      openWorkingSystemDetails (_item) {
        window.location.href = './workingSystemDetails.html?id=' + _item.id
      },
    }
  })
</script>

</body>

</html>
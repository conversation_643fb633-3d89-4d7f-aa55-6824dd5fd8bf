<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>代表联络站</title>
  <style type="text/css">
    * {
      margin: 0;
      padding: 0;
    }

    body {
      background-color: #f4f6f8;
    }

    .body_box {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .leave {
      position: relative;
      margin: 5vw 5vw 0 5vw;
      height: 7.5vw;
      padding: 2vw 3.75vw;
      border-radius: 2.5vw 2.5vw 0 0;
      font-size: 4.5vw;
      font-weight: 700;
      background: #fff;
    }

    .dbAbsoluteButtom {
      position: absolute;
      top: 23px;
      left: 13px;
    }

    .users {
      background-color: #fff;
      padding: 4vw;
      margin: 0 5vw;
      border-radius: 0 0 2.5vw 2.5vw;
      height: 27vw;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .user-left {
      width: 21vw;
      height: 26.75vw;
      border: 1px solid #eff2f5;
      border-radius: 1vw;
    }

    .headImgStatusStyle {
      position: absolute;
      background-color: #f37824;
      border-radius: 0px 20px 20px 0px;
      color: #fff;
      font-size: 12px;
      width: 40px;
      height: 20px;
    }

    .user-left .img {
      border-radius: 4px;
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }

    .user-center {
      padding: 5px 0;
      height: 120px;
      width: 70%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .user-center-cen {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .name {
      height: 22px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333333;
    }

    .names {
      margin-bottom: 7.5vw;
      color: #999;
      font-size: 3.5vw;
      font-family: PingFang SC;
      font-weight: 700;
    }

    .user-center-btn {
      display: flex;
      align-items: center;
    }

    .user-right {
      display: flex;
      align-items: center;
      font-size: 3.5vw;
      color: #3894ff;
    }

    .user-right .img {
      width: 4vw;
      margin-right: 1.25vw;
    }

    .report {
      /* position: relative; */
    }

    .records {
      min-height: 97px;
      margin: 10px 20px;
      background: #ffffff;
      border-radius: 10px;
    }

    .title {
      padding: 10px 0 0 0;
      position: relative;
      font-size: 18px;
      font-weight: bold;
      margin-left: 20px;
      width: auto;
      height: 30px;
    }

    .rectangle {
      position: absolute;
      top: 23px;
      left: -2px;
    }

    .datalist {
      padding: 2.5vw 5vw 0 5vw;
    }

    .record {
      width: 100%;
      height: 80px;
      background: #ffffff;
    }

    .dates {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 10px;
    }

    .titles {
      width: 70%;
      min-height: 8vw;
      font-size: 4vw;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 8vw;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      color: #333;
    }

    .btn {
      width: 15vw;
      height: 7vw;
      text-align: center;
      background: hsla(0, 0%, 60%, .1);
      border-radius: 2px;
      font-size: 3.5vw;
      font-weight: 400;
      line-height: 7vw;
      color: #999;
    }

    .btns {
      width: 44px;
      height: 20px;
      text-align: center;
      background: rgba(56, 148, 255, 0.1);
      border-radius: 2px;
      font-size: 3.5vw;
      font-weight: 400;
      line-height: 7vw;
      color: #3894ff;
      padding: 5px;
    }

    .record-flex {
      color: #666666;
      display: flex;
      justify-content: space-between;
    }

    .record-user {
      font-size: 14px;
    }

    .record-user .img {
      margin: 5px 5px -2px 0;
      width: 15px;
    }

    .record-time {
      font-size: 14px;
    }

    .record-time .img {
      margin: 5px 5px -2px 0;
      width: 15px;
    }

    .bor {
      border-bottom: 0.5px solid #ebebeb;
    }

    [v-cloak] {
      display: none
    }

    .loading {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      text-align: center;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <div class="leave">
      代表信息
      <img class="dbAbsoluteButtom" src="../img/Rectangle.png" alt="" />
    </div>
    <div class="users">
      <div class="user-left" v-cloak>
        <div class="headImgStatusStyle" v-show="headImgStatus">值班中</div>
        <img v-if="details.headImg" :src="details.headImg" alt="" class="img">
        <img v-else src="../img/gpx_def_head_img.jpg" alt="" style="width: 100%;" class="img">
      </div>
      <div class="user-center">
        <div class="user-center-cen">
          <div class="name" v-cloak>{{details.userName}}</div>
        </div>
        <div class="user-center-cen">
          <div class="names" style="color: #9e9e9e;" v-cloak>{{details.areaTypeName}}</div>
        </div>
        <div class="user-center-btn">
          <div class="user-right" @click="toQrCode">
            <img src="../img/qrCode.png" alt="" class="img" />
            <span class="qrCodeName">我要留言</span>
          </div>
        </div>
      </div>
    </div>
    <div class="report">
      <div class="records">
        <div class="title">
          <img class="rectangle" src="../img/Rectangle.png" alt="" />
          留言列表
        </div>
        <div class="loading" v-if="loadingShow"></div>
        <template v-else-if="details.wygzsReplyTitles&&details.wygzsReplyTitles.length!==0">
          <div v-for="item in details.wygzsReplyTitles" :key="item.id" @click="toDetails(item)" class="datalist">
            <div class="record bor" v-cloak>
              <div class="dates">
                <div class="titles">{{item.title}}</div>
                <div :class="item.status === '0' ? 'btn' : 'btns'">{{ item.status === '0' ? '未回复' : '已回复' }}</div>
              </div>
              <div class="record-flex">
                <div class="record-user">
                  <img src="../img/peoples.png" alt="" class="img" />{{item.name}}
                </div>
                <div class="record-time">
                  <img src="../img/alarm-clock.png" alt="" class="img" />
                  {{item.reflectDate.substr(0,16)}}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>
  </div>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
  <script>
    var privateKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
    var publicResidentKey = '040f9c525cb303564a3bb491cee3fb7f64358695dac865e11aed1b319bd7d1c9e859ebd7456fda1d9d46cd4dbeb7b2a57f97f756a0ee8820cab6b4c99bf6e8dd63' 	// 获取驻站代表公钥
    var userId = ''
    var studioId = ''
    var app = new Vue({
      el: "#app",
      data: {
        headImgStatus: false, // tru显示值班中,false不显示
        details: {},
        commentList: [
          // { id: '1', name: '姓名', title: '标题', reflectDate: '2024-04-30 09:56:25' },
          // { id: '1', name: '姓名', title: '标题', reflectDate: '2024-04-30 09:56:25' },
          // { id: '1', name: '姓名', title: '标题', reflectDate: '2024-04-30 09:56:25' }
        ],
        loadingShow: true
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search);
        userId = urlParams.get('userId')
        studioId = urlParams.get('studioId')
        if (userId && studioId) {
          this.getResidentInfo()
        }
      },
      methods: {
        // 获取代表详情
        getResidentInfo () {
          var that = this
          let headers = {
            "U-Login-Areaid": '370200'
          }
          var interfacecontent = { "userId": userId, "studioId": studioId, areaId: localStorage.getItem('areaId') }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicResidentKey)
          let fdDqtq = new FormData()
          fdDqtq.append('app_id', 'dbllztuhek')
          fdDqtq.append('interface_id', 'qdrdllzResidentRepresentativeInfo')
          fdDqtq.append('version', '1.0')
          fdDqtq.append('biz_content', biz_content)
          fdDqtq.append('charset', 'utf-8')
          fdDqtq.append('timestamp', (new Date()).valueOf())
          fdDqtq.append('origin', '1')
          fdDqtq.append('sign', 'signResult')
          fdDqtq.append('header', JSON.stringify(headers))
          $.ajax({
            url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
            type: 'post',
            dataType: 'json',
            data: fdDqtq,
            contentType: false,
            processData: false,
            cache: false,
            success: function (data) {
              try {
                var ret = JSON.parse(SM.decrypt(data.data, privateKey))
                console.log('获取详情==>>>', ret)
                if (ret.errcode == 200) {
                  that.details = ret.data
                  that.loadingShow = false
                } else {
                  that.details = {}
                }
              } catch (error) {
                // 在这里处理异常情况
                console.log('解析JSON时发生错误：', error);
                that.getResidentInfo()
              }
            },
            error: function (data) {
              console.log('请求失败了！', data)
            }
          })
        },
        // 进留言列表详情
        toDetails (_item) {
          console.log('进留言列表详情')
          window.location.href = './massMessagesDetails.html?id=' + _item.id
        },
        // 点击我要留言
        toQrCode () {
          console.log('我要留言')
          window.location.href = './submitMessages.html?title=' + this.details.userName + '&studioId=' + studioId + '&userId=' + this.details.userId
        }
      }
    })
  </script>
</body>

</html>
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>代表联络站</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <style type="text/css">
    * {
      margin: 0;
      padding: 0;
    }

    body {
      background: #fff;
    }

    .body_box {
      padding: 4vw;
    }

    .title {
      width: 100%;
      min-height: 10.5vw;
      font-size: 4.5vw;
      font-weight: 500;
      line-height: 6.5vw;
      color: #333;
    }

    .dateStyle {
      height: auto;
      width: 100%;
      display: flex;
      justify-content: flex-start;
    }

    .puchDateStyle {
      font-size: 3.5vw;
      font-weight: 400;
      color: #999;
      margin: 1.75vw 0 0 3vw;
    }

    .center {
      width: 100%;
    }

    .center .text {
      width: 100%;
      font-size: 4vw;
      font-family: PingFang SC;
      font-weight: 400;
      color: #666;
    }

    .center .text img {
      width: 350px !important;
      height: auto !important;
      vertical-align: middle;
      margin: 10px auto !important;
      display: block !important;
    }

    .center .text p {
      font-size: 16px !important;
      background-color: #fff !important;
      margin-top: 2.5vw !important;
      /* width: 360px !important; */
    }

    .center img {
      max-width: 360px !important;
      height: auto;
      vertical-align: middle;
    }

    .center .img {
      width: 100%;
      height: 229px;
      margin-top: 10px;
    }

    .center .img img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }

    [v-cloak] {
      display: none
    }

    .nodata {
      margin-top: 30px;
      text-align: center;
      color: #ccc;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <div class="title" v-cloak>{{details.name}}</div>
    <div class="dateStyle">
      <div class="puchDateStyle" v-cloak>{{details.puchDate}}</div>
    </div>
    <div class="center">
      <div class="text" v-html="details.content"></div>
    </div>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
  <script>
    var privateKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
    var publicActivityInfoKey = '04f29743b93744f5c16de8071974be322ae50025d900de52866919ddfe299dd58f27d718307ccc5f4f2d78b4e6953d7e3568e639b039b0736ecccabad67c64055f' 	// 获取活动动态详情公钥
    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        details: {
          // name: '参加“三基”工程社区联席会议', puchDate: '2023-06-02 17:34:32', content: "<p>&nbsp; &nbsp; &nbsp; &nbsp; 6月2日上午，为持续推进“三基”工程落实，市人大代表、青岛商务学校正高级讲师徐璟和区人大代表、青岛粥全粥到伟业酒店管理有限公司董事长李梦飞在团岛社区参加“三基”工程社区联席会议，认真听取社区近期重点工作推进情况，同社区各级负责同志交流意见，并就“人大代表听民声 点亮居民微心愿”主题活动商议具体方案，进一步关爱并帮助辖区内的老人和孩子，推动辖区内一老一小真正感受到人大代表的温暖和关爱。</p><p>&nbsp; &nbsp; &nbsp; &nbsp;会议结束后，两位代表在社区接待居民，认真听取关于老旧楼院改造、辖区老人业余爱好拓展缺乏资源等问题的意见和建议，认真记录并交流解决措施，积极发挥人大代表作用。</p><p style=\"line-height: 2em;\"><br/></p>"
        }
      },
      async mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id')
        if (id) {
          this.getInfo()
        }
      },
      methods: {
        // 获取活动动态详情
        getInfo () {
          var that = this
          let headers = {
            "U-Login-Areaid": '370200'
          }
          var interfacecontent = { "id": id }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicActivityInfoKey)
          let fdDqtq = new FormData()
          fdDqtq.append('app_id', 'dbllztuhek')
          fdDqtq.append('interface_id', 'qdrdllzActivityInfo')
          fdDqtq.append('version', '1.0')
          fdDqtq.append('biz_content', biz_content)
          fdDqtq.append('charset', 'utf-8')
          fdDqtq.append('timestamp', (new Date()).valueOf())
          fdDqtq.append('origin', '1')
          fdDqtq.append('sign', 'signResult')
          fdDqtq.append('header', JSON.stringify(headers))
          $.ajax({
            url: "https://isdapp.shandong.gov.cn/jpaas-jags-server/interface/gateway",
            type: 'post',
            dataType: 'json',
            data: fdDqtq,
            contentType: false,
            processData: false,
            cache: false,
            success: function (data) {
              try {
                var ret = JSON.parse(SM.decrypt(data.data, privateKey))
                console.log('获取活动动态详情===>', ret)
                that.details = ret.data
              } catch (error) {
                // 在这里处理异常情况
                console.log('解析JSON时发生错误：', error);
                that.getInfo()
              }
            },
            error: function (data) {
              console.log('请求失败了！', data)
              that.getInfo()
            }
          })
        },
      }
    })
  </script>
</body>

</html>